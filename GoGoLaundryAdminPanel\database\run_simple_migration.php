<?php
/**
 * Simple Shop Sync System Migration Runner
 * This script runs the shop synchronization system database migration
 */

// Include database connection
require_once '../includes/functions.php';

try {
    echo "<h2>GoGoLaundry Shop Sync System - Database Migration</h2>\n";
    echo "<p>Starting database migration for shop synchronization system...</p>\n";

    // Read the simple migration SQL file
    $migrationFile = __DIR__ . '/migrations/shop_sync_system_simple.sql';

    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }

    $sql = file_get_contents($migrationFile);

    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }

    echo "<p>Migration file loaded successfully.</p>\n";

    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );

    echo "<p>Found " . count($statements) . " SQL statements to execute.</p>\n";

    // Execute statements
    $pdo->beginTransaction();
    $successCount = 0;
    $errors = [];

    foreach ($statements as $index => $statement) {
        try {
            if (!empty(trim($statement))) {
                $pdo->exec($statement);
                $successCount++;
                echo "<p style='color: green;'>✅ Statement " . ($index + 1) . " executed successfully</p>\n";
            }
        } catch (PDOException $e) {
            $error = "Statement " . ($index + 1) . " failed: " . $e->getMessage();
            $errors[] = $error;
            echo "<p style='color: red;'>❌ $error</p>\n";
        }
    }

    if (empty($errors)) {
        // Now create triggers and procedures manually
        echo "<h4>Creating triggers and procedures...</h4>\n";
        
        // Create triggers for shop_services
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS shop_services_update_trigger");
            $pdo->exec("
                CREATE TRIGGER shop_services_update_trigger 
                AFTER INSERT ON shop_services
                FOR EACH ROW
                INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
                VALUES (NEW.shop_id, 'service_update', NOW())
                ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
            ");
            echo "<p style='color: green;'>✅ Trigger 'shop_services_update_trigger' created</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Trigger 'shop_services_update_trigger' failed: " . $e->getMessage() . "</p>\n";
        }

        try {
            $pdo->exec("DROP TRIGGER IF EXISTS shop_services_update_trigger_update");
            $pdo->exec("
                CREATE TRIGGER shop_services_update_trigger_update 
                AFTER UPDATE ON shop_services
                FOR EACH ROW
                INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
                VALUES (NEW.shop_id, 'service_update', NOW())
                ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
            ");
            echo "<p style='color: green;'>✅ Trigger 'shop_services_update_trigger_update' created</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Trigger 'shop_services_update_trigger_update' failed: " . $e->getMessage() . "</p>\n";
        }

        // Create triggers for shop_items
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS shop_items_update_trigger");
            $pdo->exec("
                CREATE TRIGGER shop_items_update_trigger 
                AFTER INSERT ON shop_items
                FOR EACH ROW
                INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
                VALUES (NEW.shop_id, 'item_update', NOW())
                ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
            ");
            echo "<p style='color: green;'>✅ Trigger 'shop_items_update_trigger' created</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Trigger 'shop_items_update_trigger' failed: " . $e->getMessage() . "</p>\n";
        }

        try {
            $pdo->exec("DROP TRIGGER IF EXISTS shop_items_update_trigger_update");
            $pdo->exec("
                CREATE TRIGGER shop_items_update_trigger_update 
                AFTER UPDATE ON shop_items
                FOR EACH ROW
                INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
                VALUES (NEW.shop_id, 'item_update', NOW())
                ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0
            ");
            echo "<p style='color: green;'>✅ Trigger 'shop_items_update_trigger_update' created</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ Trigger 'shop_items_update_trigger_update' failed: " . $e->getMessage() . "</p>\n";
        }

        // Create view for shop sync status
        try {
            $pdo->exec("DROP VIEW IF EXISTS shop_sync_status");
            $pdo->exec("
                CREATE VIEW shop_sync_status AS
                SELECT 
                    ls.id as shop_id,
                    ls.name as shop_name,
                    ls.updated_at as last_profile_update,
                    MAX(CASE WHEN sun.update_type = 'service_update' THEN sun.created_at END) as last_service_update,
                    MAX(CASE WHEN sun.update_type = 'item_update' THEN sun.created_at END) as last_item_update,
                    MAX(CASE WHEN sun.update_type = 'pricing_update' THEN sun.created_at END) as last_pricing_update,
                    COUNT(CASE WHEN sun.is_processed = 0 THEN 1 END) as pending_sync_count,
                    MAX(sun.created_at) as last_update_notification
                FROM laundry_shops ls
                LEFT JOIN shop_update_notifications sun ON ls.id = sun.shop_id
                GROUP BY ls.id, ls.name, ls.updated_at
            ");
            echo "<p style='color: green;'>✅ View 'shop_sync_status' created</p>\n";
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ View 'shop_sync_status' failed: " . $e->getMessage() . "</p>\n";
        }

        $pdo->commit();
        echo "<h3 style='color: green;'>✅ Migration completed successfully!</h3>\n";
        echo "<p>$successCount statements executed successfully.</p>\n";

        // Verify tables were created
        echo "<h4>Verifying created tables:</h4>\n";
        $tables = [
            'shop_update_notifications',
            'api_cache_invalidation',
            'shop_location_history'
        ];

        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    echo "<p style='color: green;'>✅ Table '$table' created successfully</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠️ Table '$table' not found</p>\n";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Error checking table '$table': " . $e->getMessage() . "</p>\n";
            }
        }

        echo "<h4>Testing the sync system:</h4>\n";
        
        // Test notification insertion
        try {
            $testStmt = $pdo->prepare("
                INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
                VALUES (1, 'profile_update', NOW())
                ON DUPLICATE KEY UPDATE created_at = NOW()
            ");
            $testStmt->execute();
            echo "<p style='color: green;'>✅ Test notification inserted successfully</p>\n";
            
            // Clean up test data
            $pdo->exec("DELETE FROM shop_update_notifications WHERE shop_id = 1 AND update_type = 'profile_update'");
            echo "<p style='color: green;'>✅ Test data cleaned up</p>\n";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ Error testing sync system: " . $e->getMessage() . "</p>\n";
        }

        echo "<h4>Next Steps:</h4>\n";
        echo "<ol>\n";
        echo "<li>✅ Database migration completed successfully</li>\n";
        echo "<li>🔄 Test the admin panel location updates</li>\n";
        echo "<li>📱 Test the Android app sync functionality</li>\n";
        echo "<li>🔍 Monitor the sync_status API endpoint</li>\n";
        echo "</ol>\n";

        echo "<h4>API Endpoints:</h4>\n";
        echo "<ul>\n";
        echo "<li><a href='../api/shops/sync_status.php?action=check_updates' target='_blank'>Check Shop Updates</a></li>\n";
        echo "<li><a href='../api/shops/sync_status.php?action=sync_status' target='_blank'>Get Sync Status</a></li>\n";
        echo "<li><a href='../api/shops/sync_status.php?action=pending_notifications' target='_blank'>Get Pending Notifications</a></li>\n";
        echo "</ul>\n";

        echo "<h4>Test Admin Panel:</h4>\n";
        echo "<ul>\n";
        echo "<li><a href='../laundryshop/profile.php' target='_blank'>Shop Profile (with Interactive Map)</a></li>\n";
        echo "<li><a href='../laundryshop/services.php' target='_blank'>Shop Services</a></li>\n";
        echo "<li><a href='../laundryshop/items.php' target='_blank'>Shop Items</a></li>\n";
        echo "<li><a href='../laundryshop/pricing.php' target='_blank'>Shop Pricing</a></li>\n";
        echo "</ul>\n";

    } else {
        $pdo->rollback();
        echo "<h3 style='color: red;'>❌ Migration failed!</h3>\n";
        echo "<p>Errors encountered:</p>\n";
        echo "<ul>\n";
        foreach ($errors as $error) {
            echo "<li style='color: red;'>$error</li>\n";
        }
        echo "</ul>\n";
        echo "<p>$successCount out of " . count($statements) . " statements executed successfully before failure.</p>\n";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Fatal Error: " . $e->getMessage() . "</p>\n";
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollback();
    }
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f5f5;
}

h2, h3, h4 {
    color: #333;
}

p, li {
    margin: 5px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
