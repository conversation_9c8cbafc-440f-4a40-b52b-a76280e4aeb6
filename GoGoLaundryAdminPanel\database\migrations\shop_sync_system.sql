-- Shop Synchronization System Database Migration
-- This migration adds tables and triggers for real-time synchronization between admin panel and mobile app

-- 1. Create shop_update_notifications table for tracking changes
CREATE TABLE IF NOT EXISTS `shop_update_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `update_type` enum('profile_update', 'service_update', 'item_update', 'pricing_update') NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  `is_processed` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  <PERSON>EY `shop_id_idx` (`shop_id`),
  KEY `update_type_idx` (`update_type`),
  KEY `processed_idx` (`is_processed`, `created_at`),
  UNIQUE KEY `shop_update_unique` (`shop_id`, `update_type`),
  FOREIG<PERSON> KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Create triggers for automatic notification creation

-- Trigger for shop_services table changes
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `shop_services_update_trigger` 
AFTER INSERT ON `shop_services`
FOR EACH ROW
BEGIN
    INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
    VALUES (NEW.shop_id, 'service_update', NOW())
    ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0;
END$$

CREATE TRIGGER IF NOT EXISTS `shop_services_update_trigger_update` 
AFTER UPDATE ON `shop_services`
FOR EACH ROW
BEGIN
    INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
    VALUES (NEW.shop_id, 'service_update', NOW())
    ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0;
END$$

-- Trigger for shop_items table changes
CREATE TRIGGER IF NOT EXISTS `shop_items_update_trigger` 
AFTER INSERT ON `shop_items`
FOR EACH ROW
BEGIN
    INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
    VALUES (NEW.shop_id, 'item_update', NOW())
    ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0;
END$$

CREATE TRIGGER IF NOT EXISTS `shop_items_update_trigger_update` 
AFTER UPDATE ON `shop_items`
FOR EACH ROW
BEGIN
    INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
    VALUES (NEW.shop_id, 'item_update', NOW())
    ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0;
END$$

-- Trigger for pricing updates (when custom_price is changed)
CREATE TRIGGER IF NOT EXISTS `shop_items_pricing_trigger` 
AFTER UPDATE ON `shop_items`
FOR EACH ROW
BEGIN
    IF OLD.custom_price != NEW.custom_price THEN
        INSERT INTO shop_update_notifications (shop_id, update_type, created_at) 
        VALUES (NEW.shop_id, 'pricing_update', NOW())
        ON DUPLICATE KEY UPDATE created_at = NOW(), is_processed = 0;
    END IF;
END$$

DELIMITER ;

-- 3. Create API cache invalidation table
CREATE TABLE IF NOT EXISTS `api_cache_invalidation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cache_key` varchar(255) NOT NULL,
  `invalidated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `reason` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `cache_key_idx` (`cache_key`),
  KEY `invalidated_at_idx` (`invalidated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Create shop location history table for tracking location changes
CREATE TABLE IF NOT EXISTS `shop_location_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shop_id` int(11) NOT NULL,
  `old_latitude` decimal(10,8) DEFAULT NULL,
  `old_longitude` decimal(11,8) DEFAULT NULL,
  `new_latitude` decimal(10,8) NOT NULL,
  `new_longitude` decimal(11,8) NOT NULL,
  `old_address` varchar(255) DEFAULT NULL,
  `new_address` varchar(255) NOT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `shop_id_idx` (`shop_id`),
  KEY `updated_at_idx` (`updated_at`),
  FOREIGN KEY (`shop_id`) REFERENCES `laundry_shops`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Create trigger for location history tracking
DELIMITER $$
CREATE TRIGGER IF NOT EXISTS `shop_location_history_trigger` 
AFTER UPDATE ON `laundry_shops`
FOR EACH ROW
BEGIN
    IF OLD.latitude != NEW.latitude OR OLD.longitude != NEW.longitude OR OLD.address != NEW.address THEN
        INSERT INTO shop_location_history (
            shop_id, 
            old_latitude, 
            old_longitude, 
            new_latitude, 
            new_longitude,
            old_address,
            new_address,
            updated_by,
            updated_at
        ) VALUES (
            NEW.id,
            OLD.latitude,
            OLD.longitude,
            NEW.latitude,
            NEW.longitude,
            OLD.address,
            NEW.address,
            'shop_owner',
            NOW()
        );
    END IF;
END$$
DELIMITER ;

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shops_location ON laundry_shops(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_shops_active_verified ON laundry_shops(is_active, is_verified);
CREATE INDEX IF NOT EXISTS idx_shop_services_available ON shop_services(shop_id, is_available);
CREATE INDEX IF NOT EXISTS idx_shop_items_available ON shop_items(shop_id, is_available);

-- 7. Create view for shop sync status
CREATE OR REPLACE VIEW `shop_sync_status` AS
SELECT 
    ls.id as shop_id,
    ls.name as shop_name,
    ls.updated_at as last_profile_update,
    MAX(CASE WHEN sun.update_type = 'service_update' THEN sun.created_at END) as last_service_update,
    MAX(CASE WHEN sun.update_type = 'item_update' THEN sun.created_at END) as last_item_update,
    MAX(CASE WHEN sun.update_type = 'pricing_update' THEN sun.created_at END) as last_pricing_update,
    COUNT(CASE WHEN sun.is_processed = 0 THEN 1 END) as pending_sync_count,
    MAX(sun.created_at) as last_update_notification
FROM laundry_shops ls
LEFT JOIN shop_update_notifications sun ON ls.id = sun.shop_id
GROUP BY ls.id, ls.name, ls.updated_at;

-- 8. Insert initial notification cleanup procedure
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS `CleanupOldNotifications`()
BEGIN
    -- Mark notifications older than 24 hours as processed
    UPDATE shop_update_notifications 
    SET is_processed = 1, processed_at = NOW()
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) 
    AND is_processed = 0;
    
    -- Delete notifications older than 7 days
    DELETE FROM shop_update_notifications 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Delete old cache invalidation records
    DELETE FROM api_cache_invalidation 
    WHERE invalidated_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
END$$
DELIMITER ;

-- 9. Create event scheduler for automatic cleanup (if events are enabled)
-- Note: This requires SUPER privileges and event_scheduler to be ON
-- SET GLOBAL event_scheduler = ON;

-- CREATE EVENT IF NOT EXISTS `cleanup_notifications_event`
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   CALL CleanupOldNotifications();
