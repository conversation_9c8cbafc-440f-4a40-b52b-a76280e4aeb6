package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.model.ShopFilter;
import com.mdsadrulhasan.gogolaundry.ui.dialog.ShopFilterDialog;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.ShopMapViewModel;

import org.osmdroid.api.IMapController;
import org.osmdroid.config.Configuration;
import org.osmdroid.tileprovider.tilesource.TileSourceFactory;
import org.osmdroid.util.GeoPoint;
import org.osmdroid.views.MapView;
import org.osmdroid.views.overlay.Marker;
import org.osmdroid.views.overlay.mylocation.GpsMyLocationProvider;
import org.osmdroid.views.overlay.mylocation.MyLocationNewOverlay;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying laundry shops on a map using OSMDroid
 */
public class ShopMapFragment extends Fragment implements ShopFilterDialog.OnFilterAppliedListener {

    private static final String TAG = "ShopMapFragment";
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    private static final double DEFAULT_ZOOM = 15.0;
    private static final double DHAKA_LAT = 23.8103;
    private static final double DHAKA_LNG = 90.4125;
    private static final long SYNC_INTERVAL_MS = 30000; // 30 seconds

    private MapView mapView;
    private ShopMapViewModel viewModel;
    private FusedLocationProviderClient fusedLocationClient;
    private MyLocationNewOverlay myLocationOverlay;
    private IMapController mapController;

    // Enhanced UI components
    private SwipeRefreshLayout swipeRefreshLayout;
    private ProgressBar loadingProgressBar;
    private TextView statusTextView;
    private ChipGroup filterChipGroup;
    private Handler syncHandler;
    private Runnable syncRunnable;
    private boolean isAutoSyncEnabled = true;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize OSMDroid configuration
        Configuration.getInstance().setUserAgentValue(requireContext().getPackageName());

        // Initialize location client
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity());

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(ShopMapViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_shop_map, container, false);

        initializeViews(view);
        setupMap();
        observeViewModel();

        return view;
    }

    private void initializeViews(View view) {
        mapView = view.findViewById(R.id.mapView);

        // Initialize enhanced UI components
        swipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        loadingProgressBar = view.findViewById(R.id.loadingProgressBar);
        statusTextView = view.findViewById(R.id.statusTextView);
        filterChipGroup = view.findViewById(R.id.filterChipGroup);

        // Initialize search components
        EditText searchEditText = view.findViewById(R.id.searchEditText);
        ImageButton filterButton = view.findViewById(R.id.filterButton);

        // Initialize floating action buttons
        FloatingActionButton currentLocationFab = view.findViewById(R.id.currentLocationFab);
        FloatingActionButton shopListFab = view.findViewById(R.id.shopListFab);

        // Setup enhanced UI components
        setupSwipeRefresh();
        setupFilterChips();
        setupAutoSync();

        // Setup search functionality
        setupSearchBar(searchEditText, filterButton);

        // Setup floating action buttons
        setupFloatingActionButtons(currentLocationFab, shopListFab);
    }

    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "Swipe refresh triggered");
                refreshShopData();
            });
            swipeRefreshLayout.setColorSchemeResources(
                R.color.colorPrimary,
                R.color.colorAccent
            );
        }
    }

    private void setupFilterChips() {
        if (filterChipGroup != null) {
            // Add filter chips dynamically
            addFilterChip("All Shops", true);
            addFilterChip("Nearby", false);
            addFilterChip("Verified", false);
            addFilterChip("High Rated", false);
        }
    }

    private void addFilterChip(String text, boolean isChecked) {
        Chip chip = new Chip(requireContext());
        chip.setText(text);
        chip.setCheckable(true);
        chip.setChecked(isChecked);
        chip.setOnCheckedChangeListener((buttonView, checked) -> {
            if (checked) {
                applyFilter(text);
            }
        });
        filterChipGroup.addView(chip);
    }

    private void applyFilter(String filterType) {
        Log.d(TAG, "Applying filter: " + filterType);
        updateStatusText("Applying filter: " + filterType);

        switch (filterType) {
            case "All Shops":
                viewModel.loadAllShops();
                break;
            case "Nearby":
                getCurrentLocationAndLoadNearbyShops();
                break;
            case "Verified":
                // Apply verified filter
                viewModel.loadAllShops(); // TODO: Add verified filter to ViewModel
                break;
            case "High Rated":
                // Apply high rated filter
                viewModel.loadAllShops(); // TODO: Add rating filter to ViewModel
                break;
        }
    }

    private void setupAutoSync() {
        syncHandler = new Handler(Looper.getMainLooper());
        syncRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAutoSyncEnabled && isAdded()) {
                    Log.d(TAG, "Auto-sync triggered");
                    checkForShopUpdates();
                    syncHandler.postDelayed(this, SYNC_INTERVAL_MS);
                }
            }
        };

        // Start auto-sync
        if (isAutoSyncEnabled) {
            syncHandler.postDelayed(syncRunnable, SYNC_INTERVAL_MS);
        }
    }

    private void checkForShopUpdates() {
        // TODO: Implement API call to check for shop updates
        // This would call the sync_status.php endpoint
        Log.d(TAG, "Checking for shop updates...");
        updateStatusText("Checking for updates...");

        // For now, just refresh the data periodically
        viewModel.refreshShopData();
    }

    private void refreshShopData() {
        Log.d(TAG, "Refreshing shop data");
        updateStatusText("Refreshing shops...");

        // Clear existing markers
        clearShopMarkers();

        // Reload shops based on current state
        if (hasLocationPermission()) {
            getCurrentLocationAndLoadShops();
        } else {
            viewModel.loadAllShops();
        }
    }

    private void updateStatusText(String status) {
        if (statusTextView != null) {
            statusTextView.setText(status);
            statusTextView.setVisibility(View.VISIBLE);

            // Hide status text after 3 seconds
            statusTextView.postDelayed(() -> {
                if (statusTextView != null) {
                    statusTextView.setVisibility(View.GONE);
                }
            }, 3000);
        }
    }

    private boolean hasLocationPermission() {
        return ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void getCurrentLocationAndLoadNearbyShops() {
        if (!hasLocationPermission()) {
            requestLocationPermissionAndSetup();
            return;
        }

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 10.0);
                    } else {
                        ToastUtils.showWarning(requireContext(), "Unable to get current location");
                        viewModel.loadAllShops();
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get location for nearby shops", e);
                    viewModel.loadAllShops();
                });
    }

    private void setupFloatingActionButtons(FloatingActionButton currentLocationFab, FloatingActionButton shopListFab) {
        Log.d(TAG, "Setting up floating action buttons");

        // Current Location FAB - Get user's current location and center map
        if (currentLocationFab != null) {
            currentLocationFab.setOnClickListener(v -> {
                Log.d(TAG, "Current location FAB clicked");
                getCurrentLocationAndCenterMap();
            });
        }

        // Shop List FAB - Toggle between map and list view
        if (shopListFab != null) {
            shopListFab.setOnClickListener(v -> {
                Log.d(TAG, "Shop list FAB clicked");
                showShopListDialog();
            });
        }
    }

    private void setupSearchBar(EditText searchEditText, ImageButton filterButton) {
        Log.d(TAG, "Setting up search bar");

        // Setup search text listener
        searchEditText.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                String query = searchEditText.getText().toString().trim();
                Log.d(TAG, "Search action triggered with query: '" + query + "'");

                if (!query.isEmpty()) {
                    Log.d(TAG, "Calling viewModel.searchShops() with query: '" + query + "'");

                    // Clear existing markers before search
                    clearShopMarkers();

                    viewModel.searchShops(query);

                    // Hide keyboard
                    android.view.inputmethod.InputMethodManager imm =
                        (android.view.inputmethod.InputMethodManager) requireActivity().getSystemService(android.content.Context.INPUT_METHOD_SERVICE);
                    if (imm != null) {
                        imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                    }
                } else {
                    Log.w(TAG, "Empty search query, not performing search");
                }
                return true;
            }
            return false;
        });

        // Setup filter button click
        filterButton.setOnClickListener(v -> {
            Log.d(TAG, "Filter button clicked, showing filter dialog");
            showFilterDialog();
        });
    }

    private void setupMap() {
        // Set tile source
        mapView.setTileSource(TileSourceFactory.MAPNIK);

        // Enable multi-touch controls
        mapView.setMultiTouchControls(true);

        // Get map controller
        mapController = mapView.getController();
        mapController.setZoom(DEFAULT_ZOOM);

        // Set default location (Dhaka, Bangladesh)
        GeoPoint defaultPoint = new GeoPoint(DHAKA_LAT, DHAKA_LNG);
        mapController.setCenter(defaultPoint);

        // Setup my location overlay
        setupMyLocationOverlay();

        // Request location permission and get current location
        requestLocationPermissionAndSetup();
    }

    private void setupMyLocationOverlay() {
        myLocationOverlay = new MyLocationNewOverlay(new GpsMyLocationProvider(requireContext()), mapView);
        myLocationOverlay.enableMyLocation();
        mapView.getOverlays().add(myLocationOverlay);
    }

    private void requestLocationPermissionAndSetup() {
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(requireActivity(),
                    new String[]{Manifest.permission.ACCESS_FINE_LOCATION},
                    LOCATION_PERMISSION_REQUEST_CODE);
        } else {
            getCurrentLocationAndLoadShops();
        }
    }

    private void getCurrentLocationAndLoadShops() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        GeoPoint userLocation = new GeoPoint(location.getLatitude(), location.getLongitude());
                        mapController.setCenter(userLocation);

                        // Load nearby shops
                        viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 10.0); // 10km radius
                    } else {
                        // Use default location and load shops
                        viewModel.loadAllShops();
                    }
                })
                .addOnFailureListener(e -> {
                    ToastUtils.showWarning(requireContext(), "Unable to get current location");
                    viewModel.loadAllShops();
                });
    }

    private void observeViewModel() {
        // Observe shops data (only once to prevent multiple calls)
        viewModel.getShops().observe(getViewLifecycleOwner(), shops -> {
            Log.d(TAG, "observeViewModel: Received shops data - " + (shops != null ? shops.size() : 0) + " shops");

            // Stop swipe refresh
            if (swipeRefreshLayout != null) {
                swipeRefreshLayout.setRefreshing(false);
            }

            if (shops != null && !shops.isEmpty()) {
                Log.d(TAG, "Adding " + shops.size() + " shop markers to map");
                for (int i = 0; i < Math.min(3, shops.size()); i++) {
                    LaundryShopEntity shop = shops.get(i);
                    Log.d(TAG, "  Shop " + (i+1) + ": " + shop.getName() + " at (" + shop.getLatitude() + ", " + shop.getLongitude() + ")");
                }
                addShopMarkersToMap(shops);
                updateStatusText("Found " + shops.size() + " shops");
            } else {
                Log.w(TAG, "No shops to display on map");
                // Clear existing markers when no shops found
                clearShopMarkers();
                updateStatusText("No shops found in this area");
            }
        });

        // Observe loading state
        viewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            Log.d(TAG, "Loading state changed: " + isLoading);

            // Show/hide loading indicator
            if (loadingProgressBar != null) {
                loadingProgressBar.setVisibility(isLoading ? View.VISIBLE : View.GONE);
            }

            // Update status text
            if (isLoading) {
                updateStatusText("Loading shops...");
            }
        });

        // Observe error messages
        viewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMessage -> {
            if (errorMessage != null && !errorMessage.isEmpty()) {
                Log.e(TAG, "Error message received: " + errorMessage);
                ToastUtils.showError(requireContext(), errorMessage);
                updateStatusText("Error: " + errorMessage);

                // Stop swipe refresh on error
                if (swipeRefreshLayout != null) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }
        });
    }

    private void clearShopMarkers() {
        Log.d(TAG, "Clearing existing shop markers");
        mapView.getOverlays().removeIf(overlay -> overlay instanceof Marker);
        mapView.invalidate();
    }

    private void addShopMarkersToMap(List<LaundryShopEntity> shops) {
        Log.d(TAG, "addShopMarkersToMap: Adding " + shops.size() + " markers");

        // Clear existing shop markers (keep my location overlay)
        clearShopMarkers();

        int markersAdded = 0;
        for (LaundryShopEntity shop : shops) {
            if (shop.getLatitude() != 0.0 && shop.getLongitude() != 0.0) {
                addShopMarker(shop);
                markersAdded++;
            } else {
                Log.w(TAG, "Shop " + shop.getName() + " has invalid coordinates: (" + shop.getLatitude() + ", " + shop.getLongitude() + ")");
            }
        }

        Log.d(TAG, "Successfully added " + markersAdded + " markers to map");

        // Refresh map
        mapView.invalidate();

        // If we have shops, zoom to fit them
        if (markersAdded > 0) {
            zoomToFitMarkers(shops);
        }
    }

    private void addShopMarker(LaundryShopEntity shop) {
        GeoPoint shopLocation = new GeoPoint(shop.getLatitude(), shop.getLongitude());
        Log.d(TAG, "Adding marker for shop: " + shop.getName() + " at " + shopLocation);

        Marker marker = new Marker(mapView);
        marker.setPosition(shopLocation);
        marker.setAnchor(Marker.ANCHOR_CENTER, Marker.ANCHOR_BOTTOM);
        marker.setTitle(shop.getName());

        // Create snippet with shop info
        String snippet = String.format("Rating: %.1f ⭐ | Distance: %.1f km\n%s",
                shop.getRating(), shop.getDistance(), shop.getAddress());
        marker.setSnippet(snippet);

        // Set marker icon (you can customize this)
        Drawable icon = ContextCompat.getDrawable(requireContext(), R.drawable.ic_shop_marker);
        if (icon != null) {
            marker.setIcon(icon);
        } else {
            Log.w(TAG, "Shop marker icon not found, using default");
        }

        // Set click listener
        marker.setOnMarkerClickListener((clickedMarker, mapView) -> {
            // Open shop details
            openShopDetails(shop);
            return true;
        });

        mapView.getOverlays().add(marker);
        Log.d(TAG, "Marker added successfully for shop: " + shop.getName());
    }

    private void zoomToFitMarkers(List<LaundryShopEntity> shops) {
        if (shops == null || shops.isEmpty()) {
            return;
        }

        try {
            double minLat = Double.MAX_VALUE;
            double maxLat = Double.MIN_VALUE;
            double minLng = Double.MAX_VALUE;
            double maxLng = Double.MIN_VALUE;

            for (LaundryShopEntity shop : shops) {
                if (shop.getLatitude() != 0.0 && shop.getLongitude() != 0.0) {
                    minLat = Math.min(minLat, shop.getLatitude());
                    maxLat = Math.max(maxLat, shop.getLatitude());
                    minLng = Math.min(minLng, shop.getLongitude());
                    maxLng = Math.max(maxLng, shop.getLongitude());
                }
            }

            if (minLat != Double.MAX_VALUE) {
                // Calculate center point
                double centerLat = (minLat + maxLat) / 2;
                double centerLng = (minLng + maxLng) / 2;
                GeoPoint center = new GeoPoint(centerLat, centerLng);

                // Set center
                mapController.setCenter(center);

                // Calculate appropriate zoom level
                double latSpan = maxLat - minLat;
                double lngSpan = maxLng - minLng;
                double maxSpan = Math.max(latSpan, lngSpan);

                int zoomLevel = (int) DEFAULT_ZOOM;
                if (maxSpan > 0.1) zoomLevel = 10;
                else if (maxSpan > 0.05) zoomLevel = 12;
                else if (maxSpan > 0.01) zoomLevel = 14;
                else zoomLevel = 16;

                mapController.setZoom(zoomLevel);
                Log.d(TAG, "Zoomed to fit " + shops.size() + " markers with zoom level: " + zoomLevel);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error zooming to fit markers", e);
        }
    }

    private void openShopDetails(LaundryShopEntity shop) {
        // Navigate to shop details fragment
        ShopDetailsFragment shopDetailsFragment = ShopDetailsFragment.newInstance(shop.getId());

        requireActivity().getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.fragment_container, shopDetailsFragment)
                .addToBackStack(null)
                .commit();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                getCurrentLocationAndLoadShops();
            } else {
                ToastUtils.showWarning(requireContext(), "Location permission denied");
                viewModel.loadAllShops();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (mapView != null) {
            mapView.onResume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mapView != null) {
            mapView.onPause();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (mapView != null) {
            mapView.onDetach();
        }
    }

    /**
     * Get current location and center map on it
     */
    private void getCurrentLocationAndCenterMap() {
        if (ActivityCompat.checkSelfPermission(requireContext(), Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            Log.w(TAG, "Location permission not granted");
            ToastUtils.showWarning(requireContext(), "Location permission required");
            return;
        }

        Log.d(TAG, "Getting current location to center map");
        fusedLocationClient.getLastLocation()
                .addOnSuccessListener(location -> {
                    if (location != null) {
                        Log.d(TAG, "Current location found: " + location.getLatitude() + ", " + location.getLongitude());
                        GeoPoint userLocation = new GeoPoint(location.getLatitude(), location.getLongitude());
                        mapController.animateTo(userLocation);
                        mapController.setZoom(16); // Zoom in closer for current location

                        // Load nearby shops around current location
                        viewModel.loadNearbyShops(location.getLatitude(), location.getLongitude(), 5.0); // 5km radius

                        ToastUtils.showSuccess(requireContext(), "Centered on your location");
                    } else {
                        Log.w(TAG, "Current location not available");
                        ToastUtils.showWarning(requireContext(), "Current location not available");
                    }
                })
                .addOnFailureListener(e -> {
                    Log.e(TAG, "Failed to get current location", e);
                    ToastUtils.showError(requireContext(), "Failed to get current location");
                });
    }

    /**
     * Show shop list dialog
     */
    private void showShopListDialog() {
        Log.d(TAG, "Showing shop list dialog");

        // Get current shops from ViewModel
        List<LaundryShopEntity> shops = viewModel.getShops().getValue();
        if (shops != null && !shops.isEmpty()) {
            // For now, show a simple message with shop count
            // TODO: Implement proper shop list dialog
            String message = "Found " + shops.size() + " shop(s) in current area";
            ToastUtils.showInfo(requireContext(), message);

            // Log shop details for debugging
            Log.d(TAG, "Current shops on map:");
            for (int i = 0; i < Math.min(5, shops.size()); i++) {
                LaundryShopEntity shop = shops.get(i);
                Log.d(TAG, "  " + (i+1) + ". " + shop.getName() + " - " + shop.getAddress());
            }
        } else {
            ToastUtils.showInfo(requireContext(), "No shops found in current area");
        }
    }

    /**
     * Show filter dialog
     */
    private void showFilterDialog() {
        ShopFilter currentFilter = viewModel.getCurrentFilter().getValue();
        if (currentFilter == null) {
            currentFilter = new ShopFilter();
        }

        ShopFilterDialog dialog = ShopFilterDialog.newInstance(currentFilter);
        dialog.show(getChildFragmentManager(), "ShopFilterDialog");
    }

    /**
     * Callback when filter is applied
     */
    @Override
    public void onFilterApplied(ShopFilter filter) {
        Log.d(TAG, "onFilterApplied() called with filter: " + filter.toString());
        Log.d(TAG, "Filter has active filters: " + filter.hasActiveFilters());

        viewModel.applyFilter(filter);

        // Show filter summary
        String summary = filter.getFilterSummary();
        Log.d(TAG, "Filter summary: " + summary);

        if (filter.hasActiveFilters()) {
            ToastUtils.showSuccess(requireContext(), "Filter applied: " + summary);
        } else {
            ToastUtils.showInfo(requireContext(), "All filters cleared");
        }

        // Update filter button appearance to show active state
        updateFilterButtonState(filter.hasActiveFilters());
    }

    /**
     * Update filter button appearance based on active state
     */
    private void updateFilterButtonState(boolean hasActiveFilters) {
        ImageButton filterButton = getView().findViewById(R.id.filterButton);
        if (filterButton != null) {
            if (hasActiveFilters) {
                // Show active state (e.g., different color or icon)
                filterButton.setColorFilter(getResources().getColor(R.color.home_accent_blue, null));
            } else {
                // Show normal state
                filterButton.setColorFilter(getResources().getColor(R.color.home_text_on_gradient, null));
            }
        }
    }

    /**
     * Factory method to create new instance
     */
    public static ShopMapFragment newInstance() {
        return new ShopMapFragment();
    }
}
